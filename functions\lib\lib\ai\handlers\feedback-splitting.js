"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.splitFeedbackFunction = void 0;
const v2_1 = require("firebase-functions/v2");
const https_1 = require("firebase-functions/v2/https");
const genkit_config_1 = require("../config/genkit-config");
const feedback_splitting_schema_1 = require("../schemas/feedback-splitting-schema");
const feedback_splitting_prompts_1 = require("../prompts/feedback-splitting-prompts");
/**
 * Analyzes feedback text and splits it into individual items if it contains multiple distinct points
 */
const splitFeedbackFunction = async (request) => {
    var _a;
    const { data, auth } = request;
    const requestId = Date.now().toString();
    v2_1.logger.debug("Feedback splitting started", {
        requestId,
        functionName: "splitFeedbackFunction",
    });
    try {
        // Validate authentication
        if (!auth) {
            v2_1.logger.warn("Unauthenticated request", { requestId });
            throw new https_1.HttpsError("unauthenticated", "The function must be called while authenticated.");
        }
        const userId = auth.uid;
        // Validate input
        if (!data.feedbackText || typeof data.feedbackText !== "string") {
            v2_1.logger.warn("Invalid input data", { requestId, userId, data });
            throw new https_1.HttpsError("invalid-argument", "feedbackText is required and must be a string.");
        }
        const feedbackText = data.feedbackText.trim();
        if (!feedbackText) {
            v2_1.logger.warn("Empty feedback text", { requestId, userId });
            throw new https_1.HttpsError("invalid-argument", "feedbackText cannot be empty.");
        }
        v2_1.logger.debug("Input data validated", {
            requestId,
            userId,
            feedbackLength: feedbackText.length,
            feedbackPreview: feedbackText.substring(0, 100) + (feedbackText.length > 100 ? "..." : ""),
        });
        v2_1.logger.info("Analyzing feedback for splitting", {
            requestId,
            userId,
        });
        v2_1.logger.debug("Creating splitting prompt", { requestId, userId });
        const prompt = (0, feedback_splitting_prompts_1.createFeedbackSplittingPrompt)(feedbackText);
        v2_1.logger.debug("Prompt created", {
            requestId,
            userId,
            promptLength: prompt.length,
        });
        v2_1.logger.debug("Calling AI model for feedback splitting", {
            requestId,
            userId,
            model: genkit_config_1.mainModel || "unknown-model",
            temperature: 0.3,
            maxOutputTokens: 1024,
        });
        const { output } = await genkit_config_1.ai.generate({
            model: genkit_config_1.mainModel,
            prompt,
            output: { schema: feedback_splitting_schema_1.FeedbackSplittingSchema },
            config: {
                temperature: 0.3,
                maxOutputTokens: 1024,
            },
        });
        if (!output) {
            throw new Error("AI model returned empty output");
        }
        const splittingResponse = output;
        v2_1.logger.debug("AI splitting response received", {
            requestId,
            userId,
            responseReceived: !!output,
            isMultiple: splittingResponse.isMultiple,
            itemsCount: splittingResponse.feedbackItems.length,
            itemTitles: splittingResponse.feedbackItems.map(item => item.title),
            totalLength: JSON.stringify(splittingResponse).length,
        });
        if (splittingResponse.isMultiple) {
            v2_1.logger.info("Multiple feedback points detected", {
                requestId,
                userId,
                itemsCount: splittingResponse.feedbackItems.length,
                itemTitles: splittingResponse.feedbackItems.map(item => item.title),
                summary: splittingResponse.summary,
            });
        }
        else {
            v2_1.logger.info("Single feedback point detected", {
                requestId,
                userId,
                itemTitle: (_a = splittingResponse.feedbackItems[0]) === null || _a === void 0 ? void 0 : _a.title,
            });
        }
        return splittingResponse;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorType = error instanceof Error ? error.constructor.name : "Unknown";
        v2_1.logger.error("Error in feedback splitting", {
            requestId,
            userId: (auth === null || auth === void 0 ? void 0 : auth.uid) || "unknown",
            error: errorMessage,
            errorType,
            stack: error instanceof Error ? error.stack : undefined,
        });
        // Re-throw HttpsError as-is, wrap others
        if (error instanceof https_1.HttpsError) {
            throw error;
        }
        throw new https_1.HttpsError("internal", "An error occurred while analyzing the feedback.");
    }
};
exports.splitFeedbackFunction = splitFeedbackFunction;
//# sourceMappingURL=feedback-splitting.js.map