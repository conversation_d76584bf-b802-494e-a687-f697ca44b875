import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Loader2, Copy } from 'lucide-react';
import { toast } from 'sonner';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/app/firebase';

// Types for the multi-story response
export interface UserStory {
  isRejected: boolean;
  title: string;
  role?: string;
  goal?: string;
  benefit?: string;
  formattedStory?: string;
  feasibility?: number;
  complexity?: number;
  priority?: string;
  featureCategory?: string;
  issueAnalysis?: string;
  rejectionReason?: string;
  duplicateIssueNumber?: number;
  duplicateIssueTitle?: string;
}

export interface MultipleStoriesResponse {
  isMultiple: true;
  stories: UserStory[];
  summary: string;
}

export interface SingleStoryResponse {
  isMultiple: false;
  story: UserStory;
}

export type StoryResponse = SingleStoryResponse | MultipleStoriesResponse;

interface MultiStoryDisplayProps {
  storyResponse: MultipleStoriesResponse;
  selectedRepo: any;
  onCreateIssue?: (story: UserStory) => Promise<void>;
}

interface StoryCardProps {
  story: UserStory;
  index: number;
  selectedRepo: any;
  onCreateIssue?: (story: UserStory) => Promise<void>;
}

const StoryCard: React.FC<StoryCardProps> = ({ story, index, selectedRepo, onCreateIssue }) => {
  const [isOpen, setIsOpen] = useState(index === 0); // First story open by default
  const [isCreatingIssue, setIsCreatingIssue] = useState(false);

  const handleCreateIssue = async () => {
    if (!onCreateIssue) return;

    setIsCreatingIssue(true);
    try {
      await onCreateIssue(story);
      toast.success(`GitHub Issue für "${story.title}" wurde erfolgreich erstellt.`);
    } catch (error) {
      console.error('Error creating GitHub issue:', error);
      toast.error('Fehler beim Erstellen des GitHub Issues. Bitte versuchen Sie es erneut.');
    } finally {
      setIsCreatingIssue(false);
    }
  };

  const handleCopyStory = () => {
    let fullText = '';

    if (story.isRejected) {
      fullText = `
ABGELEHNTE USER STORY: ${story.title}

${story.rejectionReason}
${story.duplicateIssueNumber ? `Duplicate Issue #${story.duplicateIssueNumber}: ${story.duplicateIssueTitle}` : ''}
      `.trim();
    } else {
      fullText = `
${story.title}

${story.formattedStory}

Bewertung:
- Machbarkeit: ${story.feasibility}/5
- Komplexität: ${story.complexity}/10
- Priorität: ${story.priority}
- Feature-Kategorie: ${story.featureCategory}

Details:
Rolle: ${story.role}
Ziel: ${story.goal}
Nutzen: ${story.benefit}

${story.issueAnalysis ? `Issue-Analyse: ${story.issueAnalysis}` : ''}
      `.trim();
    }

    navigator.clipboard.writeText(fullText);
    toast.success('User Story in die Zwischenablage kopiert');
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <Card className="w-full shadow-sm border-l-4 border-l-blue-500">
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-3 border-b cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {isOpen ? (
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
                <div>
                  <CardTitle className="text-lg font-bold">{story.title}</CardTitle>
                  <CardDescription className="mt-1 text-sm">
                    {story.isRejected ? 'Abgelehnte User Story' : `User Story ${index + 1}`}
                  </CardDescription>
                </div>
              </div>
              {!story.isRejected && (
                <Badge className={
                  story.priority === 'Critical' ? 'bg-red-100 text-red-800 hover:bg-red-200' :
                  story.priority === 'High' ? 'bg-orange-100 text-orange-800 hover:bg-orange-200' :
                  story.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
                  'bg-green-100 text-green-800 hover:bg-green-200'
                }>
                  {story.priority}
                </Badge>
              )}
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-4 space-y-4">
            {story.isRejected ? (
              <div className="p-4 rounded-lg bg-red-50 border border-red-200">
                <h4 className="text-sm font-medium text-red-800 mb-2">Ablehnungsgrund</h4>
                <p className="text-sm text-red-700 whitespace-pre-line">{story.rejectionReason}</p>
                {story.duplicateIssueNumber && (
                  <p className="text-sm text-red-600 mt-2">
                    Duplikat von Issue #{story.duplicateIssueNumber}: {story.duplicateIssueTitle}
                  </p>
                )}
              </div>
            ) : (
              <>
                <div className="p-3 rounded-lg bg-muted/40 border">
                  <p className="text-sm whitespace-pre-line leading-relaxed">{story.formattedStory}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Rolle</h4>
                    <p className="text-sm">{story.role}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Ziel</h4>
                    <p className="text-sm">{story.goal}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Nutzen</h4>
                    <p className="text-sm">{story.benefit}</p>
                  </div>
                </div>

                <Separator className="my-1" />

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-xs font-medium text-muted-foreground mb-1">MACHBARKEIT</h4>
                    <p className="text-lg font-bold">{story.feasibility}/5</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-xs font-medium text-muted-foreground mb-1">KOMPLEXITÄT</h4>
                    <p className="text-lg font-bold">{story.complexity}/10</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-xs font-medium text-muted-foreground mb-1">PRIORITÄT</h4>
                    <p className="text-sm font-medium">{story.priority}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-xs font-medium text-muted-foreground mb-1">KATEGORIE</h4>
                    <p className="text-sm font-medium">{story.featureCategory}</p>
                  </div>
                </div>

                {story.issueAnalysis && (
                  <div className="p-3 rounded-lg border bg-blue-50">
                    <h4 className="text-sm font-medium mb-2">Issue-Analyse</h4>
                    <p className="text-sm text-muted-foreground">{story.issueAnalysis}</p>
                  </div>
                )}
              </>
            )}
          </CardContent>

          <CardFooter className="py-3 border-t flex justify-end space-x-3">
            {!story.isRejected && selectedRepo && (
              <Button
                variant="default"
                size="sm"
                onClick={handleCreateIssue}
                disabled={isCreatingIssue}
              >
                {isCreatingIssue ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Erstelle Issue...
                  </>
                ) : (
                  'GitHub Issue erstellen'
                )}
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={handleCopyStory}>
              <Copy className="mr-2 h-4 w-4" />
              Kopieren
            </Button>
          </CardFooter>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
};

const MultiStoryDisplay: React.FC<MultiStoryDisplayProps> = ({
  storyResponse,
  selectedRepo,
  onCreateIssue
}) => {
  return (
    <Card className="w-full shadow-sm overflow-hidden">
      <CardHeader className="pb-3 border-b">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-xl font-bold">
              {storyResponse.stories.length} User Stories generiert
            </CardTitle>
            <CardDescription className="mt-1 text-sm">
              Das Feedback wurde in mehrere User Stories aufgeteilt
            </CardDescription>
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {storyResponse.stories.length} Stories
          </Badge>
        </div>
        {storyResponse.summary && (
          <div className="mt-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
            <h4 className="text-sm font-medium text-blue-800 mb-1">Aufteilung</h4>
            <p className="text-sm text-blue-700">{storyResponse.summary}</p>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-4 space-y-4">
        {storyResponse.stories.map((story, index) => (
          <StoryCard
            key={index}
            story={story}
            index={index}
            selectedRepo={selectedRepo}
            onCreateIssue={onCreateIssue}
          />
        ))}
      </CardContent>
    </Card>
  );
};

export default MultiStoryDisplay;
