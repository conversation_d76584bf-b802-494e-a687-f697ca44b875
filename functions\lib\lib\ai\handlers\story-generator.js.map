{"version": 3, "file": "story-generator.js", "sourceRoot": "", "sources": ["../../../../src/lib/ai/handlers/story-generator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iEAAmD;AACnD,8CAA+C;AAC/C,2DAAwD;AACxD,sEAKuC;AACvC,4DAA6D;AAE7D;;GAEG;AACH,qFAAqF;AACrF,MAAM,yBAAyB,GAAG,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;IAClE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAExC,WAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;QACvC,SAAS;QACT,YAAY,EAAE,2BAA2B;KAC1C,CAAC,CAAC;IAEH,IAAI;QACF,oDAAoD;QACpD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC7B,WAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACrD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,iBAAiB,EACjB,yBAAyB,CAC1B,CAAC;SACH;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,WAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QAE1D,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;YAC/D,WAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,SAAS;gBACT,MAAM;gBACN,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY;aACtC,CAAC,CAAC;YACH,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,2BAA2B,CAC5B,CAAC;SACH;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;QAE/C,WAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACnC,SAAS;YACT,MAAM;YACN,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACxC,eAAe,EACb,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBACnC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/C,oBAAoB,EAAE,cAAc,CAAC,MAAM;YAC3C,mBAAmB,EAAE,aAAa,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEH,WAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;YACjD,SAAS;YACT,MAAM;YACN,iBAAiB,EAAE,CAAC,CAAC,cAAc;YACnC,gBAAgB,EAAE,CAAC,CAAC,aAAa;SAClC,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,IAAA,iCAAiB,EAC9B,IAAI,CAAC,YAAY,EACjB,cAAc,EACd,aAAa,CACd,CAAC;QACF,WAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC7B,SAAS;YACT,MAAM;YACN,YAAY,EACV,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,uBAAuB;SACvE,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC/B,SAAS;YACT,MAAM;YACN,KAAK,EAAE,yBAAS,IAAI,eAAe;YACnC,WAAW,EAAE,GAAG;YAChB,eAAe,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC;YACnC,KAAK,EAAE,yBAAS;YAChB,MAAM;YACN,MAAM,EAAE,EAAE,MAAM,EAAE,wCAAmB,EAAE;YACvC,MAAM,EAAE;gBACN,WAAW,EAAE,GAAG;gBAChB,eAAe,EAAE,IAAI,EAAE,iCAAiC;aACzD;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QAED,MAAM,aAAa,GAAG,MAAuB,CAAC;QAE9C,qCAAqC;QACrC,IAAI,IAAA,0CAAqB,EAAC,aAAa,CAAC,EAAE;YACxC,WAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC7C,SAAS;gBACT,MAAM;gBACN,gBAAgB,EAAE,CAAC,CAAC,MAAM;gBAC1B,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK;gBACrC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM;gBACvD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;aAC9C,CAAC,CAAC;YAEH,WAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE;gBACtD,SAAS;gBACT,MAAM;gBACN,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK;aACtC,CAAC,CAAC;SACJ;aAAM,IAAI,IAAA,8CAAyB,EAAC,aAAa,CAAC,EAAE;YACnD,WAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACjD,SAAS;gBACT,MAAM;gBACN,gBAAgB,EAAE,CAAC,CAAC,MAAM;gBAC1B,YAAY,EAAE,aAAa,CAAC,OAAO,CAAC,MAAM;gBAC1C,WAAW,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;gBACtD,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM;gBACjD,OAAO,EAAE,aAAa,CAAC,OAAO;aAC/B,CAAC,CAAC;YAEH,WAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE;gBAC1D,SAAS;gBACT,MAAM;gBACN,YAAY,EAAE,aAAa,CAAC,OAAO,CAAC,MAAM;gBAC1C,WAAW,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;aACvD,CAAC,CAAC;SACJ;QAED,OAAO,aAAa,CAAC;KACtB;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,MAAM,SAAS,GACb,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9D,WAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;YAChC,SAAS;YACT,SAAS;YACT,YAAY;YACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,SAAS;YACT,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QAEH,8DAA8D;QAC9D,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE;YAC/C,MAAM,KAAK,CAAC;SACb;aAAM;YACL,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;SAChE;KACF;AACH,CAAC,CAAC;AAGO,8DAAyB"}