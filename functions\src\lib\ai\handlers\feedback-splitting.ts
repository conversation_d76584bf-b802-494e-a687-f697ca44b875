import { logger } from "firebase-functions/v2";
import { CallableRequest, HttpsError } from "firebase-functions/v2/https";
import { ai, mainModel } from "../config/genkit-config";
import { FeedbackSplittingSchema, FeedbackSplittingResponse } from "../schemas/feedback-splitting-schema";
import { createFeedbackSplittingPrompt } from "../prompts/feedback-splitting-prompts";

/**
 * Analyzes feedback text and splits it into individual items if it contains multiple distinct points
 */
const splitFeedbackFunction = async (request: CallableRequest) => {
  const { data, auth } = request;
  const requestId = Date.now().toString();

  logger.debug("Feedback splitting started", {
    requestId,
    functionName: "splitFeedbackFunction",
  });

  try {
    // Validate authentication
    if (!auth) {
      logger.warn("Unauthenticated request", { requestId });
      throw new HttpsError(
        "unauthenticated",
        "The function must be called while authenticated."
      );
    }

    const userId = auth.uid;

    // Validate input
    if (!data.feedbackText || typeof data.feedbackText !== "string") {
      logger.warn("Invalid input data", { requestId, userId, data });
      throw new HttpsError(
        "invalid-argument",
        "feedbackText is required and must be a string."
      );
    }

    const feedbackText = data.feedbackText.trim();
    if (!feedbackText) {
      logger.warn("Empty feedback text", { requestId, userId });
      throw new HttpsError(
        "invalid-argument",
        "feedbackText cannot be empty."
      );
    }

    logger.debug("Input data validated", {
      requestId,
      userId,
      feedbackLength: feedbackText.length,
      feedbackPreview: feedbackText.substring(0, 100) + (feedbackText.length > 100 ? "..." : ""),
    });

    logger.info("Analyzing feedback for splitting", {
      requestId,
      userId,
    });

    logger.debug("Creating splitting prompt", { requestId, userId });
    const prompt = createFeedbackSplittingPrompt(feedbackText);
    logger.debug("Prompt created", {
      requestId,
      userId,
      promptLength: prompt.length,
    });

    logger.debug("Calling AI model for feedback splitting", {
      requestId,
      userId,
      model: mainModel || "unknown-model",
      temperature: 0.3, // Lower temperature for more consistent splitting decisions
      maxOutputTokens: 1024,
    });

    const { output } = await ai.generate({
      model: mainModel,
      prompt,
      output: { schema: FeedbackSplittingSchema },
      config: {
        temperature: 0.3, // Lower temperature for more consistent analysis
        maxOutputTokens: 1024,
      },
    });

    if (!output) {
      throw new Error("AI model returned empty output");
    }

    const splittingResponse = output as FeedbackSplittingResponse;

    logger.debug("AI splitting response received", {
      requestId,
      userId,
      responseReceived: !!output,
      isMultiple: splittingResponse.isMultiple,
      itemsCount: splittingResponse.feedbackItems.length,
      itemTitles: splittingResponse.feedbackItems.map(item => item.title),
      totalLength: JSON.stringify(splittingResponse).length,
    });

    if (splittingResponse.isMultiple) {
      logger.info("Multiple feedback points detected", {
        requestId,
        userId,
        itemsCount: splittingResponse.feedbackItems.length,
        itemTitles: splittingResponse.feedbackItems.map(item => item.title),
        summary: splittingResponse.summary,
      });
    } else {
      logger.info("Single feedback point detected", {
        requestId,
        userId,
        itemTitle: splittingResponse.feedbackItems[0]?.title,
      });
    }

    return splittingResponse;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorType = error instanceof Error ? error.constructor.name : "Unknown";

    logger.error("Error in feedback splitting", {
      requestId,
      userId: auth?.uid || "unknown",
      error: errorMessage,
      errorType,
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Re-throw HttpsError as-is, wrap others
    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      "An error occurred while analyzing the feedback."
    );
  }
};

export { splitFeedbackFunction };
