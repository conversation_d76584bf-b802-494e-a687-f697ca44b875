"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateUserStoryFunction = void 0;
const v2_1 = require("firebase-functions/v2");
const https_1 = require("firebase-functions/v2/https");
const genkit_config_1 = require("../config/genkit-config");
const story_schema_1 = require("../schemas/story-schema");
const story_prompts_1 = require("../prompts/story-prompts");
/**
 * Transforms user feedback into a structured user story
 */
// Instead of exporting the function directly, create it to be exported from index.ts
const generateUserStoryFunction = async (request) => {
    const { data, auth } = request;
    const requestId = Date.now().toString();
    v2_1.logger.debug("Story generation started", {
        requestId,
        functionName: "generateUserStoryFunction",
    });
    try {
        // Authentication is automatically handled by onCall
        if (!auth) {
            v2_1.logger.warn("Authentication missing", { requestId });
            throw new https_1.HttpsError("unauthenticated", "Authentication required");
        }
        const userId = auth.uid;
        v2_1.logger.debug("User authenticated", { requestId, userId });
        // Basic validation
        if (!data.feedbackText || typeof data.feedbackText !== "string") {
            v2_1.logger.warn("Invalid feedback text", {
                requestId,
                userId,
                feedbackProvided: !!data.feedbackText,
            });
            throw new https_1.HttpsError("invalid-argument", "Feedback text is required");
        }
        const projectContext = data.projectContext || "";
        const issuesContext = data.issuesContext || "";
        v2_1.logger.debug("Input data validated", {
            requestId,
            userId,
            feedbackLength: data.feedbackText.length,
            feedbackPreview: data.feedbackText.substring(0, 100) +
                (data.feedbackText.length > 100 ? "..." : ""),
            projectContextLength: projectContext.length,
            issuesContextLength: issuesContext.length,
        });
        v2_1.logger.info("Generating user story from feedback", {
            requestId,
            userId,
            hasProjectContext: !!projectContext,
            hasIssuesContext: !!issuesContext,
        });
        v2_1.logger.debug("Creating prompt", { requestId, userId });
        const prompt = (0, story_prompts_1.createStoryPrompt)(data.feedbackText, projectContext, issuesContext);
        v2_1.logger.debug("Prompt created", {
            requestId,
            userId,
            promptLength: typeof prompt === "string" ? prompt.length : "complex-prompt-object",
        });
        v2_1.logger.debug("Calling AI model", {
            requestId,
            userId,
            model: genkit_config_1.mainModel || "unknown-model",
            temperature: 0.7,
            maxOutputTokens: 1024,
        });
        const { output } = await genkit_config_1.ai.generate({
            model: genkit_config_1.mainModel,
            prompt,
            output: { schema: story_schema_1.UserStorySchema },
            config: {
                temperature: 0.7,
                maxOutputTokens: 1024,
            },
        });
        if (!output) {
            throw new Error("AI model returned empty output");
        }
        const userStory = output;
        v2_1.logger.debug("AI response received", {
            requestId,
            userId,
            responseReceived: !!output,
            storyTitle: userStory.title,
            storyLength: JSON.stringify(userStory).length,
            storyFields: Object.keys(userStory),
        });
        v2_1.logger.info("User story generated successfully", {
            requestId,
            userId,
            storyTitle: userStory.title,
        });
        return output;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorType = error instanceof Error ? error.constructor.name : "Unknown";
        v2_1.logger.debug("Exception details", {
            requestId,
            errorType,
            errorMessage,
            stack: error instanceof Error ? error.stack : undefined,
        });
        v2_1.logger.error("Error generating user story", {
            requestId,
            error: errorMessage,
            stack: error instanceof Error ? error.stack : undefined,
        });
        // onCall automatically handles error forwarding to the client
        if (error instanceof https_1.HttpsError) {
            throw error;
        }
        else {
            throw new https_1.HttpsError("internal", errorMessage);
        }
    }
};
exports.generateUserStoryFunction = generateUserStoryFunction;
//# sourceMappingURL=story-generator.js.map